import { api } from '@services/api';
import { storageAuthTokenGet, storageAuthTokenRemove, storageAuthTokenSave } from '@storage/storageAuthToken';
import { storageUserGet, storageUserRemove, storageUserSave } from '@storage/storageUser';
import { router, useRootNavigationState, useSegments } from 'expo-router';
import { useToast } from 'native-base';
import React, { createContext, useEffect, useContext, useState, ReactNode } from 'react';
import { UserTypes } from 'src/@types/user';
import { OneSignal } from 'react-native-onesignal';
import { oneSignalStorage } from '@storage/storageOneSignal';
import * as SplashScreen from 'expo-splash-screen';


type AuthContextProviderProps = {
	children: ReactNode
}

const AuthContext = createContext(null);


export function useAuth() {
	return useContext(AuthContext);
}


function useProtectedRoute(user: UserTypes) {
	const segments = useSegments();

	const navigationState = useRootNavigationState();
	useEffect(() => {
		if (!navigationState?.key) return;
		const inAuthGroup = segments[0] === '(auth)';
		if (
			!user &&
			!inAuthGroup
		) {
			router.replace('/sign-in');
		} else if (user && inAuthGroup) {
			router.replace('/home');
		}
	}, [user, segments, navigationState]);
}

const getOneSinalId = async () => {
	const userId = await OneSignal.User.pushSubscription.getIdAsync();
	await oneSignalStorage.user.save(userId);

	// TODO: Implementar a chamada para a API
	try {
		await api.put('/v1/students/onesignal', { userOneSignalKey: userId });
	} catch (error) { }
};



export function AuthProvider({ children }: AuthContextProviderProps) {
	const [isLoadingUserStorageData, setIsLoadingUserStorageData] = useState(true);
	const [user, setUser] = useState(null);

	useProtectedRoute(user);

	async function storageUserAndTokenSave(userData: UserTypes, token: string) {
		try {
			setIsLoadingUserStorageData(true)
			await storageUserSave(userData);
			await storageAuthTokenSave({ token });

		} catch (error) {
			throw error
		} finally {
			setIsLoadingUserStorageData(false);
		}
	}

	async function userAndTokenUpdate(userData: UserTypes, token: string) {
		api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

		setUser(userData);
	}

	async function handleSignIn(email: string, password: string, toast: any) {

		try {
			const { data } = await api.post('/v1/sessions', { email, password });

			if (data.user && data.token) {
				await storageUserAndTokenSave(data.user, data.token.token);
				userAndTokenUpdate(data.user, data.token.token)
				getOneSinalId()
			}
		} catch (error: any) {
			toast.show({ description: error.message, placement: 'top' });
			throw error
		}
	}

	async function loadUserData() {
		try {
			setIsLoadingUserStorageData(true);

			const userLogged = await storageUserGet();
			const { token } = await storageAuthTokenGet();

			if (token && userLogged) {
				userAndTokenUpdate(userLogged, token);
			}
		} catch (error) {
			throw error
		} finally {
			setIsLoadingUserStorageData(false);
		}
	}

	async function signOut() {
		try {
			setIsLoadingUserStorageData(true);
			setUser(null);
			await storageUserRemove();
			await storageAuthTokenRemove();
		} catch (error) {
			throw error;
		} finally {
			setIsLoadingUserStorageData(false);
		}
	}

	useEffect(() => {
		loadUserData()
	}, [])

	useEffect(() => {
		const subscribe = api.registerInterceptTokenManager(signOut);

		return () => {
			subscribe();
		}
	}, [])

	// Hide splash screen when user data loading is complete
	useEffect(() => {
		if (!isLoadingUserStorageData) {
			SplashScreen.hideAsync();
		}
	}, [isLoadingUserStorageData])


	return (
		<AuthContext.Provider
			value={{
				signIn: (email, password, toast) => handleSignIn(email, password, toast),
				signOut: () => signOut(),
				user,
			}}>
			{children}
		</AuthContext.Provider>
	)
}
